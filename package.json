{"private": true, "author": "liuyongfeng <<EMAIL>>", "scripts": {"build": "cross-env UMI_ENV=production umi build", "build:dev": "cross-env UMI_ENV=dev umi build", "build:sit": "cross-env UMI_ENV=sit umi build", "build:uat": "cross-env UMI_ENV=uat umi build", "build:production": "cross-env UMI_ENV=production umi build", "dev": "cross-env UMI_ENV=dev umi dev", "postinstall": "umi setup", "lint": "umi lint --fix", "setup": "umi setup", "start": "npm run dev"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["umi lint", "prettier --write"], "*.ts?(x)": ["umi lint", "prettier --parser=typescript --write"]}, "dependencies": {"@douyinfe/semi-ui": "^2.75.0", "@types/file-saver": "^2.0.7", "file-saver": "^2.0.5", "html-docx-js": "^0.3.1", "jszip": "^3.10.1", "mitt": "^3.0.1", "react-masonry-css": "^1.0.16", "react-pdf": "^9.2.1", "react-player": "^3.3.1", "rehype-mathjax": "^7.1.0", "remark-math": "^6.0.0", "slate": "^0.112.0", "slate-dom": "^0.112.2", "slate-history": "^0.110.3", "slate-react": "^0.112.1", "umi": "^4.4.6", "umi-plugin-keep-alive": "^0.0.1-beta.35", "umi-request": "^1.4.0", "vditor": "^3.10.9"}, "devDependencies": {"@types/node": "^24.0.14", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@types/react-syntax-highlighter": "^15.5.13", "@umijs/lint": "^4.4.6", "@umijs/plugins": "^4.4.6", "classnames": "^2.5.1", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "eslint": "8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "lint-staged": "^15.4.3", "prettier": "2.8.8", "stylelint": "^14", "tailwindcss": "^3", "typescript": "^5.0.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}