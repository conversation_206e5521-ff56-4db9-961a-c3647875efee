import { RefreshIcon } from '@/assets/office';
import { BackTopIcon, DownloadIcon, MeetingAvatarIcon } from '@/assets/svg';
import GlobalSearch from '@/components/GlobalSearch';
import RightPane from '@/components/RightPane';
import type { AiSummaryType, DialogueRecordType } from '@/services/meeting';
import { fetchMeetDownLoad, fetchMeetTaskInfoById, meetSummary } from '@/services/meeting';
import { downloadFile } from '@/utils';
import { Button, Spin, Tooltip, Typography } from '@douyinfe/semi-ui';
import React, { useEffect, useRef, useState } from 'react';
import { useSearchParams, useSelector } from 'umi';
import AudioTranscriptPlayer, { TranscriptSegment } from './components/AudioTranscriptPlayer';
import styles from './index.less';

const demoAudio =
  'https://sit-mino.bhidi.com/ai-file-pars/ai/portal/cadd2666c5571e12fafdb21697b26aef6f196a5a9c28e708129870167679991d.mp3';

const demoTranscript: TranscriptSegment[] = [
  { id: 's1', start: 0, end: 5, text: '大家好，欢迎参加今天的需求讨论会。', speaker: '主持人' },
  {
    id: 's2',
    start: 5,
    end: 12,
    text: '本次会议的目标是确定 MVP 的范围和时间计划。',
    speaker: '主持人',
  },
  { id: 's3', start: 12, end: 22, text: '首先我们回顾一下上周遗留的问题。', speaker: '产品' },
  {
    id: 's4',
    start: 22,
    end: 35,
    text: '接口联调时遇到鉴权失败，已定位为网关配置问题。',
    speaker: '后端',
  },
  { id: 's5', start: 35, end: 48, text: 'UI 最新稿已上传，包含移动端适配方案。', speaker: '设计' },
  { id: 's6', start: 48, end: 62, text: '前端会在本周完成表单与校验逻辑开发。', speaker: '前端' },
  { id: 's7', start: 62, end: 76, text: '测试计划下周一开始，覆盖主要业务流程。', speaker: '测试' },
  {
    id: 's8',
    start: 76,
    end: 90,
    text: '如果没有其他问题，我们进入下一项议程。',
    speaker: '主持人',
  },
  {
    id: 's9',
    start: 90,
    end: 100,
    text: '有问题的，接口测试人员可以快速定位问题。',
    speaker: '后端',
  },
  {
    id: 's10',
    start: 100,
    end: 110,
    text: '好的，谢谢。',
    speaker: '主持人',
  },
  {
    id: 's11',
    start: 110,
    end: 115,
    text: '嗯嗯',
    speaker: '后端',
  },
  {
    id: 's12',
    start: 115,
    end: 120,
    text: '好的',
    speaker: '主持人',
  },
  {
    id: 's13',
    start: 120,
    end: 125,
    text: '嗯嗯，接口测试人员可以快速定位问题，接口测试人员可以快速定位问题。',
    speaker: '后端',
  },
  {
    id: 's14',
    start: 125,
    end: 130,
    text: '好的',
    speaker: '主持人',
  },
  {
    id: 's15',
    start: 130,
    end: 140,
    text: '是吗，我们研发人员一定要做好安全检测',
    speaker: '架构师',
  },
  {
    id: 's16',
    start: 140,
    end: 145,
    text: '是这样的',
    speaker: '主持人',
  },
  {
    id: 's17',
    start: 145,
    end: 155,
    text: '好的',
    speaker: '后端',
  },
  {
    id: 's18',
    start: 155,
    end: 160,
    text: '好的',
    speaker: '主持人',
  },
];

const MeetingSummary: React.FC = () => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [searchParams] = useSearchParams();
  const meetingId = searchParams.get('id') || '';

  // 对话记录
  const [dialogueRecord, setDialogueRecord] = useState<DialogueRecordType[]>([]);
  const [audioUrl, setAudioUrl] = useState('');
  const [aiSummary, setAiSummary] = useState<AiSummaryType>();
  const [loading, setLoading] = useState(false);
  const pageMode: string = useSelector(
    (state: { pageLayout: { mode: '' } }) => state.pageLayout.mode,
  );

  const chatContainerHide: boolean = useSelector(
    (state: { pageLayout: { chatContainerHide: false } }) => state.pageLayout.chatContainerHide,
  );

  const getMeetingSummaryConfig = () => [
    {
      configKey: 'keyWords',
      name: '关键词',
      data: aiSummary?.keyWords || [],
      render: (config: any) => <KeywordsRender {...config} />,
    },
    {
      configKey: 'meetSummary',
      name: '会议概要',
      data: aiSummary?.meetSummary || '',
      render: (config: any) => <TextContentRender {...config} />,
    },
    {
      configKey: 'issues',
      name: '关键议题',
      data: aiSummary?.issues || '',
      render: (config: any) => <TextContentRender {...config} />,
    },
    {
      configKey: 'spokesSummary',
      name: '关键摘要',
      data: aiSummary?.spokesSummary || [],
      render: (config: any) => <KeySummaryRender {...config} />,
    },
  ];

  // 通过id获取会议详情
  const getMeetTaskInfoById = async (id: string) => {
    try {
      const res = await fetchMeetTaskInfoById({ id });
      if (res.data) {
        // TODO: details其中的开始时间，AudioTranscriptPlayer组件中需要的是秒级的
        setDialogueRecord(res.data?.details || []);
        setAudioUrl(res.data?.audioUrl || '');
        setAiSummary(res.data?.aiSummary || {});
      }
    } catch (error) {
      console.log('获取会议详情失败');
    }
  };

  useEffect(() => {
    getMeetTaskInfoById(meetingId);
  }, [pageMode]);

  const handleDownload = async () => {
    try {
      const res = await fetchMeetDownLoad({ id: meetingId });
      if (res.data) {
        downloadFile(res.data.url, res.data.fileName);
      }
    } catch (error) {
      console.log('fetchMeetDownLoad', error);
    }
  };

  const handleRefresh = async () => {
    setLoading(true);
    try {
      const res = await meetSummary({ id: meetingId });
      if (res.data) {
        setAiSummary(res.data);
      }
    } catch (error) {
      console.log('meetSummary', error);
    } finally {
      setLoading(false);
    }
  };

  const getWrapStyle = function () {
    let s: Record<string, any> = {};
    if (['meeting'].indexOf(pageMode) > -1) {
      // s.paddingTop = 56;
      s.maxWidth = 'none';
    }
    if (chatContainerHide) s.display = 'none';
    return s;
  };

  // 关键词模块
  const KeywordsRender = (renderData: any) => {
    const { key, ...rest } = renderData; // 提取 key 并忽略它
    return (
      <div className={styles.keywordsRender}>
        <p className={styles.renderTitle}>{rest.name || ''}</p>
        <div className={styles.renderContent}>
          {rest.data && rest.data.length > 0
            ? rest.data.map((a: any, index: number) => (
                <div key={index} className={styles.keywordsRenderTag}>
                  {a}
                </div>
              ))
            : ''}
        </div>
      </div>
    );
  };

  // 会议概要、关键议题模块
  const TextContentRender = (renderData: any) => {
    const { key, ...rest } = renderData;
    return (
      <div className={styles.summaryRender}>
        <p className={styles.renderTitle}>{rest.name || ''}</p>
        <Typography.Text
          ellipsis={{
            rows: 4,
            expandable: true,
            collapsible: true,
            expandText: '展开全部',
            collapseText: '收起',
            // onExpand: (bool, e) => console.log(bool, e),
          }}
          style={{
            whiteSpace: 'pre-wrap',
            wordWrap: 'break-word',
            wordBreak: 'break-all',
            overflowWrap: 'break-word',
          }}
        >
          {rest.data || ''}
        </Typography.Text>
      </div>
    );
  };

  // 关键摘要模块
  const KeySummaryRender = (renderData: any) => {
    const { key, ...rest } = renderData;
    return (
      <div className={styles.keySummaryRender}>
        <p className={styles.renderTitle}>{rest.name || ''}</p>
        {rest.data && rest.data.length > 0
          ? rest.data.map((item: any, index: number) => (
              <div className={styles.transcriptItem} key={index}>
                <div className={styles.transcriptMeta}>
                  <MeetingAvatarIcon />
                  <span className={styles.transcriptSpeaker}>{item?.spkName || ''}</span>
                </div>
                <div className={styles.transcriptSummaryText}>{item?.spkSummary}</div>
              </div>
            ))
          : ''}
      </div>
    );
  };

  const scrollToTop = () => {
    const container = containerRef.current;
    if (container) container.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToBottom = () => {
    const container = containerRef.current;
    if (container) container.scrollTo({ top: container.scrollHeight, behavior: 'smooth' });
  };

  const RightContainerRender = () => {
    const config = getMeetingSummaryConfig();
    return (
      pageMode === 'meeting' && (
        <RightPane
          contentRender={() => (
            <div className={styles.meetingSummaryRightPane} ref={containerRef}>
              <p className={styles.title}>智能总结</p>
              {config.map((item, index) => {
                return (
                  <div className={styles.container} key={item.configKey}>
                    {item.render(item)}
                  </div>
                );
              })}
              {/* 滚动控制按钮 */}
              <div className={styles.scrollControls}>
                <BackTopIcon className={styles.scrollIcon} onClick={scrollToTop} />
                <BackTopIcon
                  className={[`${styles.scrollIcon} ${styles.scrollIconBottom}`]}
                  onClick={scrollToBottom}
                />
              </div>
            </div>
          )}
          leftActionRender={() => (
            <Tooltip content={'刷新'}>
              <Button theme="borderless" icon={<RefreshIcon />} onClick={handleRefresh}></Button>
            </Tooltip>
          )}
          rightActionRender={() => (
            <Tooltip content={'下载'}>
              <Button theme="borderless" icon={<DownloadIcon />} onClick={handleDownload}></Button>
            </Tooltip>
          )}
        />
      )
    );
  };

  return (
    <div className={styles.meetingSummary}>
      <div className={styles.meetingSummaryContainer} style={getWrapStyle()}>
        {/* 全局关键字搜索 */}
        {['meeting'].indexOf(pageMode) > -1 && (
          <div className={styles.meetingSummaryHeader}>
            <GlobalSearch
              className={styles.meetingSummaryGlobalSearch}
              contentArea={`.${styles.meetingSummaryContainer}`}
            />
          </div>
        )}
        {/* 音频字幕播放 */}
        <AudioTranscriptPlayer audioSrc={audioUrl} transcript={dialogueRecord} />
      </div>
      {/* 右屏智能总结展示 */}
      {RightContainerRender()}
      {loading && (
        <div className={styles.spinLoading}>
          <Spin size="large" />
        </div>
      )}
    </div>
  );
};

export default MeetingSummary;
