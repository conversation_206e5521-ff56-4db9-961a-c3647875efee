.generateTable {
  width: 800px;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    .text {
      color: #005bf8;
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: 500;
      border-bottom: 2px solid #005bf8;
    }
    .icon {
      width: 16px;
      height: 16px;
      cursor: pointer;
      transition: transform 0.3s ease;
      margin-right: 8px;
    }
  }
  .emptyMessageContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .emptyImage {
      width: 160px;
      height: 160px;
    }
    .emptyMessageText {
      font-family: PingFang SC;
      font-size: 14px;
      color: #3d3d3d;
      margin-top: 12px;
    }
  }
  :global {
    .semi-table-thead > tr > th {
      height: 54px;
      line-height: 54px;
      background: #f7f8fa !important;
      border-bottom: 0 !important;
      padding-top: 0 !important;
      padding-bottom: 0 !important;
      font-family: PingFang SC !important;
      font-size: 14px !important;
      font-weight: 500 !important;
      color: #000 !important;
    }
    .semi-table-body {
    }
    .semi-table-tbody > tr > td {
      height: 54px;
      line-height: 54px;
      padding-top: 0 !important;
      padding-bottom: 0 !important;
      color: #555 !important;
      font-family: PingFang SC !important;
      font-size: 14px !important;
      font-weight: normal !important;
    }
    .semi-table-wrapper > .semi-spin > .semi-spin-wrapper {
      color: var(--semi-color-primary) !important;
    }
    .semi-table-column-sorter {
      margin-top: -32px;
    }
  }
}

// .TablespinIcon {
//   width: 10px;
//   height: 10px;
//   animation: rotate360 2s linear infinite;
// }

.rotate {
  animation: rotate360 1s linear;
}

.spanColor {
  color: #005bf8 !important;
  cursor: pointer;
}

@keyframes rotate360 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
