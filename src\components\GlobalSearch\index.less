.globalSearchContainer {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 320px;

  :global(.semi-input-clearbtn) {
    margin-right: 0 !important;
  }

}

.globalSearchInputClear {
  background-color: #fff !important;

  &::before {
    content: '';
    width: 1px;
    height: 14px;
    background: #D8D8D8;
    margin-right: 12px;
  }

  :global(.semi-button-content) {
    font-size: 14px;
    color: #999;
    font-weight: normal !important;
  }
}

.globalSearchInput {
  flex: 1;
  font-size: 14px;
  outline: none;
  width: 320px;
  background-color: #fff !important;
  border: 1px solid #005BF8 !important;
  box-sizing: content-box;
  border-radius: 4px !important;

  :global(.semi-input-prefix) {
    padding: 0 8px;
  }
}

.globalSearchInput:focus {
  border-color: #4096ff;
}

.globalSearchControls {
  display: flex;
  align-items: center;

  &::before {
    content: '';
    width: 1px;
    height: 14px;
    background: #D8D8D8;
  }

  :global(.semi-button-borderless:not(.semi-button-disabled):hover) {
    color: #187aff !important;
    font-weight: bold !important;
    background-color: #fff !important;
  }
}

.searchNavBtn {
  padding: 8px 10px !important;
  border: none;
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s;
}

.searchNavBtn:disabled {
  cursor: not-allowed;
}

.searchNavBtn:hover:not(:disabled) {
  background: #187aff;
}

.searchNavBtnRight {
  transform: rotate(180deg);
}


.searchCounter {
  color: #999;
  font-size: 14px;
  white-space: nowrap;
}

.searchHighlight {
  background-color: rgba(0, 91, 248, 15%);
  color: #000;
  padding: 0 2px;
  border-radius: 2px;
}

.searchHighlightCurrent {
  background-color: rgba(0, 91, 248, 40%);
  animation: pulse 1.5s infinite;
}

.globalSearchClearBtn {
  color: #999 !important;
  font-weight: normal !important;
  padding-left: 0 !important;

  &::before {
    content: '';
    height: 14px;
    border-left: 1px solid #D8D8D8;
    padding-right: 12px;
  }
}

.searchCounterActive {
  color: #000;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(229, 238, 254, 70%);
  }

  70% {
    box-shadow: 0 0 0 6px rgba(255, 215, 0, 0%);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(255, 215, 0, 0%);
  }
}