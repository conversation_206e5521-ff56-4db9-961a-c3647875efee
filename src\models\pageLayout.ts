import { Reducer } from 'umi';

export interface PageLayoutModelState {
  mode: '';
  showtrans: false;
  transhowori: false;
  chatContainerHide: false;
}

export interface PageLayoutModelType {
  namespace: 'pageLayout';
  state: PageLayoutModelState;
  reducers: {
    changePageMode: Reducer<PageLayoutModelState>;
    changeChatContainerHide: Reducer<PageLayoutModelState>;
    changeTransState: Reducer<PageLayoutModelState>;
  };
}

const PageLayoutModel: PageLayoutModelType = {
  namespace: 'pageLayout',

  state: {
    mode: '',
    showtrans: false,
    transhowori: false,
    chatContainerHide: false,
  },
  reducers: {
    changePageMode(state, { payload }) {
      return {
        ...state,
        mode: payload,
      };
    },

    changeChatContainerHide(state, { payload }) {
      return {
        ...state,
        chatContainerHide: payload,
      };
    },

    changeTransState(state, { payload }) {
      return {
        ...state,
        showtrans: payload.showtrans,
        transhowori: payload.transhowori,
      };
    },
  },
};

export default PageLayoutModel;
