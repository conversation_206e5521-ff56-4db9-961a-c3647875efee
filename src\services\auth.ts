import request from '@/utils/request';

export const permissionInfoApi = '/system/auth/get-permission-info';
export const authorizeApi = '/system/oauth2/authorize';

/**
 * 获取用户信息
 */
export async function getUserInfoApi() {
  return request(permissionInfoApi);
}

/**
 * 获取授权
 */
export const fetchAuthorizeInfo = (params: { client_id: string }) => {
  return request<string>(authorizeApi, {
    method: 'GET',
    params,
  });
};
