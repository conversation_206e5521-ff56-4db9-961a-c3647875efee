import {
  AudioBackwardDisabledIcon,
  AudioBackwardIcon,
  AudioForwardDisabledIcon,
  AudioForwardIcon,
  AudioMutedIcon,
  AudioNotMutedIcon,
  AudioPauseIcon,
  AudioPlayIcon,
  BackTopIcon,
  EditIcon,
  MeetingAvatarIcon,
} from '@/assets/svg';
import { DialogueRecordType, updateSpeechContent } from '@/services/meeting';
import { secondsToTimestamp } from '@/utils';
import { Button, Select, Tag, TextArea, Toast, Tooltip, Typography } from '@douyinfe/semi-ui';
import classNames from 'classnames';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import ReactPlayer from 'react-player';
import { useSelector } from 'umi';
import styles from '../index.less';

export interface TranscriptSegment {
  id: string;
  start: number; // seconds
  end: number; // seconds
  text: string;
  speaker?: string;
}

export interface AudioTranscriptPlayerProps {
  audioSrc: string;
  transcript: DialogueRecordType[];
  autoPlay?: boolean;
}

const PLAYBACK_RATES = [0.5, 1, 1.5, 2] as const;
const PLAYBACK_RATE_OPTIONS = PLAYBACK_RATES.map((rate) => ({
  label: `${rate}x`,
  value: rate,
}));
type PlaybackRate = (typeof PLAYBACK_RATES)[number];

const initialState = {
  isReady: false,
  playing: false,
  muted: false,
  volume: 1,
  playbackRate: 1 as PlaybackRate,
  played: 0,
  loaded: 0,
  duration: 0,
  seeking: false,
  loadedSeconds: 0,
  playedSeconds: 0,
};

type PlayerState = typeof initialState;

const AudioTranscriptPlayer: React.FC<AudioTranscriptPlayerProps> = ({
  audioSrc,
  transcript,
  autoPlay,
}) => {
  const { Paragraph } = Typography;
  // 播放器和DOM引用
  const playerRef = useRef<any>(null);
  const listContainerRef = useRef<HTMLDivElement | null>(null);
  const segmentRefs = useRef<Map<string, HTMLDivElement>>(new Map());

  const pageMode: string = useSelector(
    (state: { pageLayout: { mode: '' } }) => state.pageLayout.mode,
  );

  // 状态管理
  const [state, setState] = useState<PlayerState>({
    ...initialState,
    playing: !!autoPlay,
  });
  const [transcriptData, setTranscriptData] = useState<DialogueRecordType[]>([]);
  const [activeIndex, setActiveIndex] = useState<number>(-1); // 当前激活的片段索引
  const [editingId, setEditingId] = useState<string | number | null>(null);
  const [isSelectRate, setIsSelectRate] = useState<Boolean>(false);
  const [editingText, setEditingText] = useState('');
  //
  const manualClickRef = useRef(false);

  // 辅助引用
  const pendingSeekRef = useRef<number | null>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastScrollTimeRef = useRef<number>(0);
  const lastActiveIndexRef = useRef<number>(-1);

  // 初始化转录数据
  useEffect(() => {
    setTranscriptData(transcript);
  }, [transcript]);

  // TODO: 要去掉这个逻辑：按时间排序的转录片段（缓存计算结果）
  const orderedTranscript = useMemo(() => {
    return [...transcriptData].sort((a, b) => a.startTime - b.startTime);
  }, [transcriptData]);

  // 1. 查找当前时间对应的活跃片段索引
  const findActiveIndex = useCallback(
    (time: number): number => {
      if (!orderedTranscript.length) return -1;
      for (let i = 0; i < orderedTranscript.length; i++) {
        const seg = orderedTranscript[i];
        if (time >= seg.startTime && time < seg.endTime) return i; // 落在当前片段内
        if (time < seg.startTime) return Math.max(0, i - 1); // 落在前一个片段后
      }
      return orderedTranscript.length - 1; // 超出最后一个片段
    },
    [orderedTranscript],
  );

  // 2. 滚动到活跃片段（防抖处理）
  const scrollToActive = useCallback(
    (index: number, force: boolean = false) => {
      if (index < 0 || index >= orderedTranscript.length) return;
      if (!force && index === lastActiveIndexRef.current) return; // 索引未变且非强制滚动则跳过

      const seg = orderedTranscript[index];
      const node = segmentRefs.current.get(String(seg.id));
      const container = listContainerRef.current;
      if (!node || !container) return;

      // 清理之前的滚动定时器
      if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);

      scrollTimeoutRef.current = setTimeout(
        () => {
          const nodeTop = node.offsetTop;
          const nodeHeight = node.offsetHeight;
          const containerHeight = container.clientHeight;
          // 计算居中位置
          const targetScrollTop = Math.max(0, nodeTop - containerHeight / 2 + nodeHeight / 2);
          container.scrollTo({
            top: targetScrollTop,
            behavior: force ? 'auto' : 'smooth',
          });
          lastScrollTimeRef.current = Date.now();
          lastActiveIndexRef.current = index;
        },
        force ? 0 : 100,
      );
    },
    [orderedTranscript],
  );

  // 3. 自动更新活跃索引（节流控制滚动）
  useEffect(() => {
    // 如果是手动点击，暂时不自动更新activeIndex
    if (manualClickRef.current) return;

    const nextIndex = findActiveIndex(state.playedSeconds);
    if (nextIndex !== activeIndex) {
      setActiveIndex(nextIndex);
      const now = Date.now();
      if (now - lastScrollTimeRef.current >= 500) {
        scrollToActive(nextIndex);
      }
    }
  }, [state.playedSeconds, activeIndex, findActiveIndex, scrollToActive]);

  // 清理滚动定时器（避免内存泄漏）
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);
    };
  }, []);

  // 4. 安全跳转播放时间
  const safeSeek = useCallback((seconds: number) => {
    const player = playerRef.current;
    if (player?.seekTo) {
      player.seekTo(seconds, 'seconds');
      return;
    }
    const internal = player?.getInternalPlayer?.();
    if (internal && typeof (internal as any).currentTime === 'number') {
      (internal as any).currentTime = seconds;
      return;
    }
    pendingSeekRef.current = seconds; // 播放器未就绪时缓存跳转
  }, []);

  // 5. 进度条控制
  const handleSeekMouseDown = () => {
    setState((prev) => ({ ...prev, seeking: true }));
  };

  const handleSeekChange = (event: React.SyntheticEvent<HTMLInputElement>) => {
    const played = Number.parseFloat((event.target as HTMLInputElement).value);
    setState((prev) => ({ ...prev, played }));
  };

  const handleSeekMouseUp = (event: React.SyntheticEvent<HTMLInputElement>) => {
    const played = Number.parseFloat((event.target as HTMLInputElement).value);
    setState((prev) => ({ ...prev, seeking: false }));
    if (playerRef.current) {
      playerRef.current.currentTime = played * playerRef.current.duration;
    }
  };

  const handleReady = () => {
    console.log('handleReady');

    setState((prev) => ({ ...prev, isReady: true }));
  };

  // 6. 播放状态控制
  const handlePlay = useCallback(() => {
    setState((prev) => ({ ...prev, playing: true }));
  }, []);

  const handlePause = useCallback(() => {
    setState((prev) => ({ ...prev, playing: false }));
  }, []);

  const handleEnded = useCallback(() => {
    setState((prev) => ({ ...prev, playing: false }));
  }, []);

  // 7. 播放器事件处理
  const handleDurationChange = () => {
    const player = playerRef.current;
    if (player) {
      setState((prev) => ({ ...prev, duration: player.duration }));
    }
  };

  const handleProgress = () => {
    const player = playerRef.current;
    if (!player || state.seeking || !player.buffered?.length) return;
    setState((prev) => ({
      ...prev,
      loadedSeconds: player.buffered.end(player.buffered.length - 1),
      loaded: player.buffered.end(player.buffered.length - 1) / player.duration,
    }));
  };

  const handleRateChange = () => {
    const player = playerRef.current;
    if (player) {
      setState((prev) => ({ ...prev, playbackRate: player.playbackRate }));
    }
  };

  const handleTimeUpdate = () => {
    const player = playerRef.current;
    if (!player || state.seeking || !player.duration) return;
    setState((prev) => ({
      ...prev,
      playedSeconds: player.currentTime,
      played: player.currentTime / player.duration,
    }));
  };

  // 8. 音量和播放速率控制
  const handleVolumeChange = useCallback((event: React.SyntheticEvent<HTMLInputElement>) => {
    const volume = Number.parseFloat((event.target as HTMLInputElement).value);
    setState((prev) => ({ ...prev, volume }));
  }, []);

  const handleToggleMuted = useCallback(() => {
    setState((prev) => ({ ...prev, muted: !prev.muted }));
  }, []);

  const handleSetPlaybackRate = (rate: any) => {
    setIsSelectRate(true);
    setState((prev) => ({ ...prev, playbackRate: rate }));
  };

  // 9. 点击片段跳转播放
  const handleItemClick = (index: number) => {
    const seg = orderedTranscript[index];
    if (!seg) return;
    manualClickRef.current = true;
    if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current); // 清除自动滚动

    // 跳转并播放
    safeSeek(seg.startTime);
    playerRef.current.currentTime = seg.startTime;
    setState((prev) => ({
      ...prev,
      played: seg.startTime / playerRef.current.duration,
      playing: true,
    }));
    scrollToActive(index, true); // 强制滚动
    setActiveIndex(index);
    setTimeout(() => {
      manualClickRef.current = false;
    }, 500);
  };

  // 10. 上一个/下一个对话核心逻辑（按顺序导航，允许同一说话人）
  const getPrevDialog = (): { time: number; index: number } | null => {
    if (orderedTranscript.length === 0) return null;
    // 初始状态（activeIndex=-1）或第一个片段，返回null
    if (activeIndex <= 0) return null;
    const prevIndex = activeIndex - 1;
    const prevSeg = orderedTranscript[prevIndex];
    return { time: prevSeg.startTime, index: prevIndex };
  };

  const getNextDialog = (): { time: number; index: number } | null => {
    if (orderedTranscript.length === 0) return null;
    const lastIndex = orderedTranscript.length - 1;
    // 初始状态（activeIndex=-1）时，返回第一个片段
    if (activeIndex === -1) return { time: orderedTranscript[0].startTime, index: 0 };
    // 最后一个片段，返回null
    if (activeIndex >= lastIndex) return null;
    const nextIndex = activeIndex + 1;
    const nextSeg = orderedTranscript[nextIndex];
    return { time: nextSeg.startTime, index: nextIndex };
  };

  // 上一个对话处理
  const handlePrevDialog = () => {
    const result = getPrevDialog();
    if (result) {
      const { time, index } = result;
      if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);

      playerRef.current.currentTime = time;
      setState((prev) => ({
        ...prev,
        played: time / playerRef.current.duration,
        playing: true,
      }));
      scrollToActive(index, true);
      setActiveIndex(index);
    }
  };

  // 下一个对话处理
  const handleNextDialog = () => {
    const result = getNextDialog();
    if (result) {
      const { time, index } = result;
      if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);

      playerRef.current.currentTime = time;
      setState((prev) => ({
        ...prev,
        played: time / playerRef.current.duration,
        playing: true,
      }));
      scrollToActive(index, true);
      setActiveIndex(index);
    }
  };

  // 编辑相关功能
  const startEdit = async (segId: string | number, initial: string) => {
    setEditingId(segId);
    setEditingText(initial);
    // 音频停止播放
    playerRef.current.pause();
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditingText('');
  };

  const handleEditKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter') {
      if (event.shiftKey) {
        return;
      } else {
        // 普通回车，阻止默认换行行为并发送内容
        event.preventDefault();
        console.log(1, '保存');
        saveEdit();
      }
    }
  };

  const saveEdit = async () => {
    try {
      const p = {
        id: editingId,
        speechContent: editingText,
      };
      const res = await updateSpeechContent(p);
      if (res.data) {
        // TODO: 请求历史对话接口，更新列表
        setTranscriptData((prev) =>
          prev.map((s) => (s.id == editingId ? { ...s, speechContent: editingText } : s)),
        );
        setEditingId(null);
        setEditingText('');
        Toast.success('修改成功！');
      }
    } catch (e) {
      Toast.error('修改失败！');
    }
  };

  // 12. 滚动控制（简化）
  const scrollToTop = () => {
    const container = listContainerRef.current;
    if (container) container.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToBottom = () => {
    const container = listContainerRef.current;
    if (container) container.scrollTo({ top: container.scrollHeight, behavior: 'smooth' });
  };

  const RateSelectRender = (props: any) => {
    const { value } = props;
    const displayValue = Array.isArray(value)
      ? value.map((item: any) => (typeof item === 'object' ? item.label : item)).join(' / ')
      : value;

    return (
      <div className={styles.rateSelect}>
        <Tag size="large" color="white" shape="circle">
          {displayValue}
        </Tag>
      </div>
    );
  };

  return (
    <div className={styles.meetingSummaryWrapper}>
      {/* 对话列表 */}
      <div
        className={classNames(styles.transcriptContainer, {
          [styles.transcriptContainerWrap]: ['meeting'].indexOf(pageMode) > -1,
        })}
        ref={listContainerRef}
      >
        {orderedTranscript.map((seg, index) => {
          const isActive = index === activeIndex;
          const isEditing = seg.id === editingId;
          return (
            <div
              key={seg.id}
              ref={(el) => el && segmentRefs.current.set(String(seg.id), el)}
              className={`${styles.transcriptItem} ${isActive ? styles.transcriptItemActive : ''}`}
              onClick={() => !isEditing && handleItemClick(index)}
            >
              <div className={styles.transcriptMeta}>
                {/* <img src="/images/avatar.png" alt="" className={styles.transcriptAvatar} /> */}
                <MeetingAvatarIcon />
                {seg.personName && (
                  <span className={styles.transcriptSpeaker}>{seg.personName}</span>
                )}
                <span className={styles.transcriptTime}>{seg.startTimeStr}</span>
                {!isEditing ? (
                  <Button
                    className={styles.editButton}
                    icon={<EditIcon />}
                    theme="borderless"
                    onClick={(e) => {
                      e.stopPropagation();
                      startEdit(seg.id, seg.speechContent);
                    }}
                  />
                ) : (
                  <div className={styles.saveAction}>
                    <Button
                      className={styles.editButton}
                      theme="borderless"
                      type="primary"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        saveEdit();
                      }}
                    >
                      保存
                    </Button>
                    <Button
                      className={styles.editButton}
                      theme="borderless"
                      type="tertiary"
                      onClick={(e) => {
                        e.stopPropagation();
                        cancelEdit();
                      }}
                    >
                      取消
                    </Button>
                  </div>
                )}
              </div>
              {!isEditing ? (
                <Paragraph
                  className={styles.transcriptText}
                  ellipsis={{
                    rows: 5,
                    expandable: true,
                    collapsible: true,
                    expandText: '展开全部',
                    collapseText: '收起',
                    onExpand: (b, e) => e.stopPropagation(),
                  }}
                >
                  {seg.speechContent}
                </Paragraph>
              ) : (
                <TextArea
                  className={styles.editTextarea}
                  autosize
                  value={editingText}
                  onChange={setEditingText}
                  onKeyDown={handleEditKeyDown}
                  rows={3}
                  onClick={(e) => e.stopPropagation()}
                />
              )}
            </div>
          );
        })}
      </div>

      {/* 音频控制栏 */}
      <div className={styles.audioBar}>
        <div className={styles.audioControls}>
          <div className={styles.controlGroup}>
            <Button
              icon={activeIndex <= 0 ? <AudioForwardDisabledIcon /> : <AudioForwardIcon />}
              aria-label="上一个对话"
              theme="borderless"
              disabled={activeIndex <= 0}
              onClick={handlePrevDialog}
            />
            <Button
              icon={state.playing ? <AudioPauseIcon /> : <AudioPlayIcon />}
              aria-label={state.playing ? '暂停' : '播放'}
              theme="borderless"
              className={styles.playButton}
              onClick={() => setState((prev) => ({ ...prev, playing: !prev.playing }))}
            />
            <Button
              icon={
                activeIndex >= orderedTranscript.length - 1 ? (
                  <AudioBackwardDisabledIcon />
                ) : (
                  <AudioBackwardIcon />
                )
              }
              aria-label="下一个对话"
              theme="borderless"
              onClick={handleNextDialog}
              disabled={activeIndex >= orderedTranscript.length - 1}
            />
          </div>
          <input
            type="range"
            min={0}
            max={1}
            step="any"
            value={state.played}
            // 动态设置背景渐变
            style={{
              background: `linear-gradient(
                to right,
                #005BF8 ${state.played * 100}%,
                #ebeef2 ${state.played * 100}%
              )`,
            }}
            onMouseDown={handleSeekMouseDown}
            onChange={handleSeekChange}
            onMouseUp={handleSeekMouseUp}
            className={styles.progressSlider}
          />
          <div className={styles.timeInfo}>
            <span>{secondsToTimestamp(state.playedSeconds)}</span>
            <span> / </span>
            <span>{secondsToTimestamp(state.duration)}</span>
          </div>

          <Tooltip content={state.muted ? '已静音' : '已开启'}>
            <Button className={styles.audioButton} theme="borderless" onClick={handleToggleMuted}>
              {state.muted ? <AudioNotMutedIcon /> : <AudioMutedIcon />}
            </Button>
          </Tooltip>

          <Select
            className={styles.playbackRateSelect}
            value={state.playbackRate}
            onChange={handleSetPlaybackRate}
            triggerRender={RateSelectRender}
            optionList={PLAYBACK_RATE_OPTIONS}
          ></Select>
        </div>

        {/* 音频播放器 */}
        <ReactPlayer
          ref={playerRef}
          src={audioSrc}
          playing={state.playing}
          muted={state.muted}
          volume={state.volume}
          playbackRate={state.playbackRate}
          height={0}
          width={0}
          onReady={handleReady}
          onPlay={handlePlay}
          onPause={handlePause}
          onEnded={handleEnded}
          onDurationChange={handleDurationChange}
          onProgress={handleProgress}
          onTimeUpdate={handleTimeUpdate}
          onRateChange={handleRateChange}
          config={
            {
              file: { forceAudio: true, attributes: { preload: 'metadata' } },
            } as any
          }
        />
      </div>

      {/* 滚动控制按钮 */}
      <div className={styles.scrollControls}>
        <BackTopIcon className={styles.scrollIcon} onClick={scrollToTop} />
        <BackTopIcon
          className={[`${styles.scrollIcon} ${styles.scrollIconBottom}`]}
          onClick={scrollToBottom}
        />
      </div>
    </div>
  );
};

export default AudioTranscriptPlayer;
