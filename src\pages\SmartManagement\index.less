.smartManagement {
  height: 100%;
  margin: 0 auto;
  min-width: 1300px;

  .filterTabs {
    display: flex;
    gap: 12px;
    margin: 24px 0;
  }

  .navButton {
    border-radius: 8px;
    height: 34px;
    padding: 6px 12px;
    transition: all 0.3s ease;
    font-size: 14px;
    line-height: 22px;
    font-weight: normal;
    color: #000;
    border: 1px solid #ebeef2;
    background-color: #fff;

    &:hover {
      transform: translateY(-2px);
      background-color: #fff;
      border: 1px solid #ebeef2;
    }
  }

  :global(.semi-button.developing) {
    cursor: pointer;
    background: #fff;
    box-sizing: border-box;
    border: 1px solid #ebeef2;
    font-size: 14px;
    color: #999;

    &:hover::after {
      content: '敬请期待';
      background: url('../../assets/chat/coming-soon.svg') no-repeat center center;
      position: absolute;
      top: -14px;
      right: 0;
      width: 56px;
      height: 20px;
      font-size: 12px;
      line-height: 18px;
      color: #999;
    }
  }

  :global(.semi-button.active) {
    background-color: #000;
    color: #fff;
  }

  :global(.semi-spin) {
    font-size: 14px;
    width: 100% !important;
    height: calc(100% - 24px) !important;

    :global(.semi-spin-wrapper) {
      color: #ccc !important;
    }
  }

  :global(.semi-spin-children) {
    width: 100%;
    height: 100%;
  }

  .emptyMain {
    height: 65%;
    justify-content: center;
  }
}

.iframeMain {
  width: 100%;
  height: calc(100% - 70px) !important;
  border-radius: 4px;

  &::slotted(body) {
    width: 800px;
  }
}
