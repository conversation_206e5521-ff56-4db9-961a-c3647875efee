import styles from '@/styles/officetemplate-common.less';
import { Button } from '@douyinfe/semi-ui';
import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { navItems } from '../navitems';

interface NavMenuProps {
  currentNav: string;
  onNavClick: (key: string) => void;
}

const NavMenu: React.FC<NavMenuProps> = ({ currentNav, onNavClick }) => {
  const [searchParams] = useSearchParams();
  const appCode = searchParams.get('appCode') || '';

  const currentNavItem = navItems.find((item) => item.key === appCode);

  const clearUrlParams = () => {
    const url = new URL(window.location.href);
    url.search = '';
    window.history.replaceState({}, '', url.toString());
  };

  const handleNavClick = (key: string) => {
    clearUrlParams();
    onNavClick(key);
  };

  return (
    <div className={styles.navContainer}>
      {currentNavItem ? (
        <div style={{ position: 'relative', display: 'inline-block' }}>
          <Button theme={'solid'} type="tertiary" className={`${styles.navButton} active`}>
            {currentNavItem?.text}
          </Button>
        </div>
      ) : (
        <>
          {navItems.map((item) => {
            // 如果当前导航项被禁用，则不允许点击
            const isDisabled = item.disabled;

            return (
              <div key={item.key} style={{ position: 'relative', display: 'inline-block' }}>
                <Button
                  theme={item.key === currentNav ? 'solid' : 'light'}
                  type="tertiary"
                  disabled={isDisabled}
                  className={`${styles.navButton} ${item.key === currentNav ? 'active' : ''} ${
                    isDisabled ? 'developing' : ''
                  }`}
                  onClick={() => !isDisabled && handleNavClick(item.key)}
                >
                  {item.text}
                </Button>
              </div>
            );
          })}
        </>
      )}
    </div>
  );
};

export default NavMenu;
