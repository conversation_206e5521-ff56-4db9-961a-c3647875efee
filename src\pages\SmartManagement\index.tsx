import Doing from '@/assets/empty.png';
import { fetchAuthorizeInfo } from '@/services/auth';
import type { MenuListItem } from '@/services/smartManagement';
import { getAccessToken } from '@/utils';
import { Empty, Spin } from '@douyinfe/semi-ui';
import { useEffect, useRef, useState } from 'react';
import FilterTabs from './components/FilterTabs';
import styles from './index.less';

const SmartManagement: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [currNavItem, setCurrNavItem] = useState<MenuListItem>();
  const [iframeUrl, setIframeUrl] = useState('');
  const iframeRef = useRef<any>();

  useEffect(() => {
    const clientId = currNavItem?.config?.clientId || '';
    if (clientId) getAuthorizeInfo(clientId);
  }, [currNavItem]);

  const getAuthorizeInfo = async (client_id: string) => {
    const res = await fetchAuthorizeInfo({ client_id });
    const { tokenFlag = false } = currNavItem?.config || {};
    const url = getIframeUrl(tokenFlag, res.data || '');
    setIframeUrl(url || '');
  };

  const getIframeUrl = (tokenFlag: boolean, url: string) => {
    if (!url) return '';
    const token = getAccessToken();
    if (tokenFlag) {
      return url.indexOf('?') > -1 ? `${url}&token=${token}` : `${url}?token=${token}`;
    }
    return url;
  };

  const handleNavChange = (item: MenuListItem | undefined) => {
    if (currNavItem?.routeId !== item?.routeId) setLoading(true);
    setCurrNavItem(item);
  };

  const handleIframeLoad = () => {
    setLoading(false);
  };

  return (
    <div className={styles.smartManagement}>
      <FilterTabs onChange={handleNavChange} />
      {currNavItem ? (
        <Spin tip="加载中..." spinning={loading}>
          <iframe
            ref={iframeRef}
            className={styles.iframeMain}
            src={iframeUrl}
            onLoad={handleIframeLoad}
          />
        </Spin>
      ) : (
        <Empty
          className={styles.emptyMain}
          image={<img src={Doing} style={{ width: 160, height: 160 }} />}
          description={
            <p className="text-[#555] font-sans text-xs mt-[-24px]">功能开发中，敬请期待...</p>
          }
        />
      )}
    </div>
  );
};

export default SmartManagement;
